#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级PDF文件下载器 - 使用Selenium处理JavaScript渲染
用于下载Morningstar网站的PDF文档
"""

import os
import time
import logging
from urllib.parse import urlparse, parse_qs
import requests

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdvancedPDFDownloader:
    def __init__(self):
        """初始化高级PDF下载器"""
        self.session = requests.Session()
        # 设置请求头，模拟真实浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/pdf,text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        })
        
    def extract_filename_from_url(self, url):
        """从URL中提取文件名信息"""
        try:
            parsed_url = urlparse(url)
            query_params = parse_qs(parsed_url.query)
            
            # 提取关键参数用于构建文件名
            client_id = query_params.get('clientid', ['unknown'])[0]
            investment_id = query_params.get('investmentid', ['unknown'])[0]
            doc_type = query_params.get('documenttype', ['unknown'])[0]
            language = query_params.get('language', ['unknown'])[0]
            
            # 构建有意义的文件名
            filename = f"morningstar_{client_id}_{investment_id}_type{doc_type}_lang{language}.pdf"
            return filename
        except Exception as e:
            logger.warning(f"无法从URL提取文件名: {e}")
            return "morningstar_document.pdf"
    
    def download_with_selenium(self, url, save_path=None, wait_time=10):
        """使用Selenium下载PDF文件"""
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.common.exceptions import TimeoutException
        except ImportError:
            logger.error("需要安装selenium: pip install selenium")
            return None
        
        if save_path is None:
            filename = self.extract_filename_from_url(url)
            save_path = os.path.join(os.getcwd(), filename)
        
        # 设置Chrome选项
        chrome_options = Options()
        chrome_options.add_argument('--headless')  # 无头模式
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # 设置下载目录
        download_dir = os.path.dirname(os.path.abspath(save_path))
        prefs = {
            "download.default_directory": download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "plugins.always_open_pdf_externally": True,
            "profile.default_content_settings.popups": 0,
            "profile.default_content_setting_values.automatic_downloads": 1
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        driver = None
        try:
            logger.info("启动Chrome浏览器...")
            driver = webdriver.Chrome(options=chrome_options)
            driver.set_page_load_timeout(30)
            
            logger.info(f"访问URL: {url}")
            driver.get(url)
            
            # 等待页面加载
            time.sleep(wait_time)
            
            # 检查页面内容
            page_source = driver.page_source
            logger.info(f"页面标题: {driver.title}")
            
            # 检查是否直接是PDF
            if driver.current_url.endswith('.pdf') or 'application/pdf' in driver.execute_script("return document.contentType || ''"):
                logger.info("检测到PDF内容，尝试下载...")
                # 直接下载当前页面内容
                pdf_content = driver.execute_script("return document.documentElement.outerHTML")
                # 这里需要特殊处理PDF内容
                return self._download_pdf_content(driver.current_url, save_path)
            
            # 查找PDF链接
            pdf_links = self._find_pdf_links(driver)
            if pdf_links:
                for link in pdf_links:
                    logger.info(f"找到PDF链接: {link}")
                    if self._download_pdf_content(link, save_path):
                        return save_path
            
            # 查找iframe中的PDF
            iframes = driver.find_elements(By.TAG_NAME, "iframe")
            for iframe in iframes:
                src = iframe.get_attribute("src")
                if src and ('.pdf' in src.lower() or 'pdf' in src.lower()):
                    logger.info(f"找到iframe中的PDF: {src}")
                    if self._download_pdf_content(src, save_path):
                        return save_path
            
            # 查找embed标签中的PDF
            embeds = driver.find_elements(By.TAG_NAME, "embed")
            for embed in embeds:
                src = embed.get_attribute("src")
                if src and ('.pdf' in src.lower() or 'pdf' in src.lower()):
                    logger.info(f"找到embed中的PDF: {src}")
                    if self._download_pdf_content(src, save_path):
                        return save_path
            
            # 如果没有找到PDF链接，保存页面源码用于调试
            debug_file = save_path.replace('.pdf', '_debug.html')
            with open(debug_file, 'w', encoding='utf-8') as f:
                f.write(page_source)
            logger.info(f"页面源码已保存到: {debug_file}")
            
            logger.warning("未找到PDF内容")
            return None
            
        except Exception as e:
            logger.error(f"Selenium下载过程中出错: {e}")
            return None
        finally:
            if driver:
                driver.quit()
    
    def _find_pdf_links(self, driver):
        """查找页面中的PDF链接"""
        pdf_links = []
        try:
            # 查找所有链接
            links = driver.find_elements(By.TAG_NAME, "a")
            for link in links:
                href = link.get_attribute("href")
                if href and ('.pdf' in href.lower() or 'pdf' in href.lower()):
                    pdf_links.append(href)
            
            # 查找JavaScript中的PDF URL
            scripts = driver.find_elements(By.TAG_NAME, "script")
            for script in scripts:
                script_content = script.get_attribute("innerHTML") or ""
                if '.pdf' in script_content.lower():
                    # 这里可以添加更复杂的正则表达式来提取PDF URL
                    import re
                    urls = re.findall(r'https?://[^\s"\'<>]+\.pdf[^\s"\'<>]*', script_content, re.IGNORECASE)
                    pdf_links.extend(urls)
        except Exception as e:
            logger.error(f"查找PDF链接时出错: {e}")
        
        return list(set(pdf_links))  # 去重
    
    def _download_pdf_content(self, url, save_path):
        """下载PDF内容"""
        try:
            logger.info(f"下载PDF内容: {url}")
            response = self.session.get(url, timeout=30, stream=True)
            response.raise_for_status()
            
            content_type = response.headers.get('content-type', '').lower()
            if 'application/pdf' in content_type:
                with open(save_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                
                # 验证PDF文件
                if self._verify_pdf_file(save_path):
                    logger.info(f"PDF文件下载成功: {save_path}")
                    return True
                else:
                    os.remove(save_path)
                    return False
            else:
                logger.warning(f"URL返回的不是PDF内容: {content_type}")
                return False
        except Exception as e:
            logger.error(f"下载PDF内容时出错: {e}")
            return False
    
    def _verify_pdf_file(self, file_path):
        """验证文件是否为有效的PDF文件"""
        try:
            with open(file_path, 'rb') as f:
                header = f.read(4)
                return header == b'%PDF'
        except Exception as e:
            logger.error(f"验证PDF文件时出错: {e}")
            return False

def main():
    """主函数"""
    # Morningstar PDF文档URL
    pdf_url = "https://doc.morningstar.com/LatestDoc.aspx?clientid=slahk&key=9a89ebce4355d1e3&language=451&investmentid=F000016MLB&documenttype=52&market=1447&investmenttype=1"
    
    # 创建高级下载器实例
    downloader = AdvancedPDFDownloader()
    
    print("🚀 开始使用Selenium下载PDF文件...")
    print("⚠️  注意：首次运行需要下载ChromeDriver，可能需要一些时间")
    
    # 使用Selenium下载PDF文件
    result = downloader.download_with_selenium(pdf_url)
    
    if result:
        print(f"\n✅ PDF文件下载成功!")
        print(f"📁 文件路径: {result}")
        print(f"📊 文件大小: {os.path.getsize(result) / 1024 / 1024:.2f} MB")
    else:
        print("\n❌ PDF文件下载失败!")
        print("💡 建议：")
        print("1. 检查网络连接")
        print("2. 确认URL是否正确")
        print("3. 检查是否需要登录或特殊权限")
        print("4. 尝试手动在浏览器中访问该URL")

if __name__ == "__main__":
    main()
