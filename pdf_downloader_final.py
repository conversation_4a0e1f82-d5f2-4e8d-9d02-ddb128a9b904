#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终PDF文件下载器
专门用于下载Morningstar网站的PDF文档
结合多种下载方法
"""

import requests
import os
import time
import webbrowser
import subprocess
import sys
from urllib.parse import urlparse, parse_qs
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FinalPDFDownloader:
    def __init__(self):
        """初始化最终PDF下载器"""
        self.session = requests.Session()
        # 设置完整的请求头，模拟真实浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'DNT': '1',
            'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"'
        })
        
    def extract_filename_from_url(self, url):
        """从URL中提取文件名信息"""
        try:
            parsed_url = urlparse(url)
            query_params = parse_qs(parsed_url.query)
            
            # 提取关键参数用于构建文件名
            client_id = query_params.get('clientid', ['unknown'])[0]
            investment_id = query_params.get('investmentid', ['unknown'])[0]
            doc_type = query_params.get('documenttype', ['unknown'])[0]
            language = query_params.get('language', ['unknown'])[0]
            
            # 构建有意义的文件名
            filename = f"morningstar_{client_id}_{investment_id}_type{doc_type}_lang{language}.pdf"
            return filename
        except Exception as e:
            logger.warning(f"无法从URL提取文件名: {e}")
            return "morningstar_document.pdf"
    
    def method1_direct_download(self, url, save_path):
        """方法1：直接下载尝试"""
        logger.info("🔄 方法1：尝试直接下载...")
        
        try:
            response = self.session.get(url, timeout=30, allow_redirects=True)
            response.raise_for_status()
            
            content_type = response.headers.get('content-type', '').lower()
            
            if 'application/pdf' in content_type or response.content.startswith(b'%PDF'):
                with open(save_path, 'wb') as f:
                    f.write(response.content)
                
                if self._verify_pdf_file(save_path):
                    logger.info("✅ 方法1成功：直接下载PDF")
                    return True
                else:
                    os.remove(save_path)
            
            logger.info("❌ 方法1失败：响应不是PDF文件")
            return False
            
        except Exception as e:
            logger.error(f"❌ 方法1失败：{e}")
            return False
    
    def method2_curl_download(self, url, save_path):
        """方法2：使用curl下载"""
        logger.info("🔄 方法2：尝试使用curl下载...")
        
        try:
            # 构建curl命令
            curl_cmd = [
                'curl',
                '-L',  # 跟随重定向
                '-o', save_path,  # 输出文件
                '--user-agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                '--header', 'Accept: application/pdf,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                '--header', 'Accept-Language: en-US,en;q=0.9',
                '--connect-timeout', '30',
                '--max-time', '60',
                url
            ]
            
            # 执行curl命令
            result = subprocess.run(curl_cmd, capture_output=True, text=True, timeout=90)
            
            if result.returncode == 0 and os.path.exists(save_path):
                if self._verify_pdf_file(save_path):
                    logger.info("✅ 方法2成功：curl下载PDF")
                    return True
                else:
                    os.remove(save_path)
                    logger.info("❌ 方法2失败：下载的文件不是PDF")
            else:
                logger.info(f"❌ 方法2失败：curl返回码 {result.returncode}")
                if result.stderr:
                    logger.info(f"curl错误信息: {result.stderr}")
            
            return False
            
        except subprocess.TimeoutExpired:
            logger.error("❌ 方法2失败：curl超时")
            return False
        except FileNotFoundError:
            logger.info("❌ 方法2失败：系统中未找到curl命令")
            return False
        except Exception as e:
            logger.error(f"❌ 方法2失败：{e}")
            return False
    
    def method3_wget_download(self, url, save_path):
        """方法3：使用wget下载"""
        logger.info("🔄 方法3：尝试使用wget下载...")
        
        try:
            # 构建wget命令
            wget_cmd = [
                'wget',
                '--output-document', save_path,
                '--user-agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                '--header', 'Accept: application/pdf,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                '--timeout', '30',
                '--tries', '3',
                '--no-check-certificate',
                url
            ]
            
            # 执行wget命令
            result = subprocess.run(wget_cmd, capture_output=True, text=True, timeout=90)
            
            if result.returncode == 0 and os.path.exists(save_path):
                if self._verify_pdf_file(save_path):
                    logger.info("✅ 方法3成功：wget下载PDF")
                    return True
                else:
                    os.remove(save_path)
                    logger.info("❌ 方法3失败：下载的文件不是PDF")
            else:
                logger.info(f"❌ 方法3失败：wget返回码 {result.returncode}")
            
            return False
            
        except subprocess.TimeoutExpired:
            logger.error("❌ 方法3失败：wget超时")
            return False
        except FileNotFoundError:
            logger.info("❌ 方法3失败：系统中未找到wget命令")
            return False
        except Exception as e:
            logger.error(f"❌ 方法3失败：{e}")
            return False
    
    def method4_powershell_download(self, url, save_path):
        """方法4：使用PowerShell下载（Windows）"""
        logger.info("🔄 方法4：尝试使用PowerShell下载...")
        
        try:
            # PowerShell命令
            ps_cmd = f'''
            $webClient = New-Object System.Net.WebClient
            $webClient.Headers.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            $webClient.Headers.Add("Accept", "application/pdf,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
            $webClient.DownloadFile("{url}", "{save_path}")
            '''
            
            # 执行PowerShell命令
            result = subprocess.run(['powershell', '-Command', ps_cmd], 
                                  capture_output=True, text=True, timeout=90)
            
            if result.returncode == 0 and os.path.exists(save_path):
                if self._verify_pdf_file(save_path):
                    logger.info("✅ 方法4成功：PowerShell下载PDF")
                    return True
                else:
                    os.remove(save_path)
                    logger.info("❌ 方法4失败：下载的文件不是PDF")
            else:
                logger.info(f"❌ 方法4失败：PowerShell返回码 {result.returncode}")
            
            return False
            
        except subprocess.TimeoutExpired:
            logger.error("❌ 方法4失败：PowerShell超时")
            return False
        except Exception as e:
            logger.error(f"❌ 方法4失败：{e}")
            return False
    
    def _verify_pdf_file(self, file_path):
        """验证文件是否为有效的PDF文件"""
        try:
            with open(file_path, 'rb') as f:
                header = f.read(4)
                return header == b'%PDF'
        except Exception as e:
            logger.error(f"验证PDF文件时出错: {e}")
            return False
    
    def download_pdf(self, url, save_path=None):
        """尝试多种方法下载PDF文件"""
        if save_path is None:
            filename = self.extract_filename_from_url(url)
            save_path = os.path.join(os.getcwd(), filename)
        
        logger.info(f"🎯 开始下载PDF文件: {url}")
        logger.info(f"📁 目标保存路径: {save_path}")
        
        # 创建保存目录
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        # 尝试多种下载方法
        methods = [
            self.method1_direct_download,
            self.method2_curl_download,
            self.method3_wget_download,
            self.method4_powershell_download
        ]
        
        for i, method in enumerate(methods, 1):
            try:
                if method(url, save_path):
                    logger.info(f"🎉 下载成功！使用方法{i}")
                    logger.info(f"📊 文件大小: {os.path.getsize(save_path) / 1024 / 1024:.2f} MB")
                    return save_path
            except Exception as e:
                logger.error(f"方法{i}执行时出错: {e}")
                continue
        
        logger.error("❌ 所有下载方法都失败了")
        return None
    
    def open_in_browser(self, url):
        """在浏览器中打开URL，供用户手动下载"""
        try:
            logger.info("🌐 在默认浏览器中打开URL...")
            webbrowser.open(url)
            return True
        except Exception as e:
            logger.error(f"打开浏览器时出错: {e}")
            return False

def main():
    """主函数"""
    # Morningstar PDF文档URL
    pdf_url = "https://doc.morningstar.com/LatestDoc.aspx?clientid=slahk&key=9a89ebce4355d1e3&language=451&investmentid=F000016MLB&documenttype=52&market=1447&investmenttype=1"
    
    print("🚀 Morningstar PDF下载器")
    print("=" * 50)
    print(f"📄 目标URL: {pdf_url}")
    print()
    
    # 创建下载器实例
    downloader = FinalPDFDownloader()
    
    # 尝试下载
    result = downloader.download_pdf(pdf_url)
    
    if result:
        print("\n" + "=" * 50)
        print("✅ PDF文件下载成功!")
        print(f"📁 文件路径: {result}")
        print(f"📊 文件大小: {os.path.getsize(result) / 1024 / 1024:.2f} MB")
        print("\n💡 提示：您可以使用PDF阅读器打开该文件")
    else:
        print("\n" + "=" * 50)
        print("❌ 自动下载失败!")
        print("\n🔧 建议的解决方案：")
        print("1. 检查网络连接")
        print("2. 确认URL是否需要登录或特殊权限")
        print("3. 尝试手动在浏览器中下载")
        print()
        
        # 提供手动下载选项
        user_input = input("是否在浏览器中打开URL进行手动下载？(y/n): ").lower().strip()
        if user_input in ['y', 'yes', '是']:
            if downloader.open_in_browser(pdf_url):
                print("✅ 已在浏览器中打开URL")
                print("📝 请在浏览器中手动下载PDF文件")
                expected_filename = downloader.extract_filename_from_url(pdf_url)
                print(f"💾 建议保存文件名: {expected_filename}")
            else:
                print("❌ 无法打开浏览器")
                print(f"🔗 请手动复制以下URL到浏览器中:")
                print(f"   {pdf_url}")

if __name__ == "__main__":
    main()
