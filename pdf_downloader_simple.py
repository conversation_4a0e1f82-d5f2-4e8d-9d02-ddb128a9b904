#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单PDF文件下载器
专门用于下载Morningstar网站的PDF文档
"""

import requests
import os
import time
import webbrowser
from urllib.parse import urlparse, parse_qs
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimplePDFDownloader:
    def __init__(self):
        """初始化简单PDF下载器"""
        self.session = requests.Session()
        # 设置完整的请求头，模拟真实浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'DNT': '1',
            'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"'
        })
        
    def extract_filename_from_url(self, url):
        """从URL中提取文件名信息"""
        try:
            parsed_url = urlparse(url)
            query_params = parse_qs(parsed_url.query)
            
            # 提取关键参数用于构建文件名
            client_id = query_params.get('clientid', ['unknown'])[0]
            investment_id = query_params.get('investmentid', ['unknown'])[0]
            doc_type = query_params.get('documenttype', ['unknown'])[0]
            language = query_params.get('language', ['unknown'])[0]
            
            # 构建有意义的文件名
            filename = f"morningstar_{client_id}_{investment_id}_type{doc_type}_lang{language}.pdf"
            return filename
        except Exception as e:
            logger.warning(f"无法从URL提取文件名: {e}")
            return "morningstar_document.pdf"
    
    def try_direct_download(self, url, save_path=None, max_retries=3):
        """尝试直接下载PDF文件"""
        if save_path is None:
            filename = self.extract_filename_from_url(url)
            save_path = os.path.join(os.getcwd(), filename)
        
        logger.info(f"尝试直接下载PDF文件: {url}")
        
        for attempt in range(max_retries):
            try:
                logger.info(f"尝试下载 (第 {attempt + 1} 次)")
                
                # 添加等待时间
                if attempt > 0:
                    wait_time = 2 + attempt
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                
                # 尝试不同的请求方式
                if attempt == 0:
                    # 第一次尝试：标准请求
                    response = self.session.get(url, timeout=30, allow_redirects=True)
                elif attempt == 1:
                    # 第二次尝试：添加Referer
                    headers = {'Referer': 'https://www.morningstar.com/'}
                    response = self.session.get(url, headers=headers, timeout=30, allow_redirects=True)
                else:
                    # 第三次尝试：模拟从搜索引擎来的访问
                    headers = {
                        'Referer': 'https://www.google.com/',
                        'Accept': 'application/pdf,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                    }
                    response = self.session.get(url, headers=headers, timeout=30, allow_redirects=True)
                
                response.raise_for_status()
                
                # 检查响应
                content_type = response.headers.get('content-type', '').lower()
                logger.info(f"响应状态码: {response.status_code}")
                logger.info(f"响应内容类型: {content_type}")
                logger.info(f"最终URL: {response.url}")
                logger.info(f"响应大小: {len(response.content)} 字节")
                
                # 检查是否为PDF
                if 'application/pdf' in content_type:
                    logger.info("检测到PDF内容类型")
                    return self._save_pdf_content(response.content, save_path)
                
                # 检查内容是否以PDF标识开始
                if response.content.startswith(b'%PDF'):
                    logger.info("检测到PDF文件头")
                    return self._save_pdf_content(response.content, save_path)
                
                # 如果是HTML，分析内容
                if 'text/html' in content_type:
                    logger.info("响应是HTML页面，分析内容...")
                    html_content = response.text
                    
                    # 保存HTML内容用于调试
                    debug_file = save_path.replace('.pdf', f'_debug_attempt_{attempt + 1}.html')
                    with open(debug_file, 'w', encoding='utf-8') as f:
                        f.write(html_content)
                    logger.info(f"HTML内容已保存到: {debug_file}")
                    
                    # 查找可能的PDF链接
                    pdf_links = self._extract_pdf_links_from_html(html_content, response.url)
                    for pdf_link in pdf_links:
                        logger.info(f"尝试下载找到的PDF链接: {pdf_link}")
                        if self._download_pdf_from_link(pdf_link, save_path):
                            return save_path
                
                logger.warning(f"第 {attempt + 1} 次尝试未能获取PDF内容")
                
            except requests.exceptions.RequestException as e:
                logger.error(f"请求错误 (第 {attempt + 1} 次): {e}")
            except Exception as e:
                logger.error(f"下载过程中发生错误 (第 {attempt + 1} 次): {e}")
        
        logger.error("所有直接下载尝试都失败了")
        return None
    
    def _save_pdf_content(self, content, save_path):
        """保存PDF内容到文件"""
        try:
            # 创建保存目录
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            with open(save_path, 'wb') as f:
                f.write(content)
            
            # 验证PDF文件
            if self._verify_pdf_file(save_path):
                logger.info(f"PDF文件保存成功: {save_path}")
                logger.info(f"文件大小: {os.path.getsize(save_path)} 字节")
                return save_path
            else:
                logger.error("保存的文件不是有效的PDF")
                os.remove(save_path)
                return None
        except Exception as e:
            logger.error(f"保存PDF文件时出错: {e}")
            return None
    
    def _verify_pdf_file(self, file_path):
        """验证文件是否为有效的PDF文件"""
        try:
            with open(file_path, 'rb') as f:
                header = f.read(4)
                return header == b'%PDF'
        except Exception as e:
            logger.error(f"验证PDF文件时出错: {e}")
            return False
    
    def _extract_pdf_links_from_html(self, html_content, base_url):
        """从HTML内容中提取PDF链接"""
        import re
        pdf_links = []
        
        try:
            # 查找各种可能的PDF链接模式
            patterns = [
                r'src=["\']([^"\']*\.pdf[^"\']*)["\']',
                r'href=["\']([^"\']*\.pdf[^"\']*)["\']',
                r'data=["\']([^"\']*\.pdf[^"\']*)["\']',
                r'url\(["\']?([^"\']*\.pdf[^"\']*)["\']?\)',
                r'["\']([^"\']*LatestDoc\.aspx[^"\']*)["\']',
                r'["\']([^"\']*\.pdf[^"\']*)["\']'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                for match in matches:
                    # 处理相对URL
                    if match.startswith('//'):
                        pdf_link = 'https:' + match
                    elif match.startswith('/'):
                        pdf_link = 'https://doc.morningstar.com' + match
                    elif not match.startswith('http'):
                        pdf_link = 'https://doc.morningstar.com/' + match
                    else:
                        pdf_link = match
                    
                    pdf_links.append(pdf_link)
            
            # 去重
            pdf_links = list(set(pdf_links))
            logger.info(f"从HTML中提取到 {len(pdf_links)} 个可能的PDF链接")
            
        except Exception as e:
            logger.error(f"提取PDF链接时出错: {e}")
        
        return pdf_links
    
    def _download_pdf_from_link(self, pdf_url, save_path):
        """从特定链接下载PDF"""
        try:
            response = self.session.get(pdf_url, timeout=30, stream=True)
            response.raise_for_status()
            
            content_type = response.headers.get('content-type', '').lower()
            if 'application/pdf' in content_type or response.content.startswith(b'%PDF'):
                with open(save_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                
                if self._verify_pdf_file(save_path):
                    return True
                else:
                    os.remove(save_path)
            
            return False
        except Exception as e:
            logger.error(f"从链接下载PDF时出错: {e}")
            return False
    
    def open_in_browser(self, url):
        """在浏览器中打开URL，供用户手动下载"""
        try:
            logger.info("在默认浏览器中打开URL...")
            webbrowser.open(url)
            return True
        except Exception as e:
            logger.error(f"打开浏览器时出错: {e}")
            return False

def main():
    """主函数"""
    # Morningstar PDF文档URL
    pdf_url = "https://doc.morningstar.com/LatestDoc.aspx?clientid=slahk&key=9a89ebce4355d1e3&language=451&investmentid=F000016MLB&documenttype=52&market=1447&investmenttype=1"
    
    # 创建下载器实例
    downloader = SimplePDFDownloader()
    
    print("🚀 开始下载PDF文件...")
    print(f"📄 目标URL: {pdf_url}")
    
    # 尝试直接下载
    result = downloader.try_direct_download(pdf_url)
    
    if result:
        print(f"\n✅ PDF文件下载成功!")
        print(f"📁 文件路径: {result}")
        print(f"📊 文件大小: {os.path.getsize(result) / 1024 / 1024:.2f} MB")
    else:
        print("\n❌ 自动下载失败!")
        print("\n💡 尝试在浏览器中打开URL进行手动下载...")
        
        if downloader.open_in_browser(pdf_url):
            print("✅ 已在浏览器中打开URL")
            print("📝 请在浏览器中手动下载PDF文件")
            print("💾 建议保存位置:", os.path.join(os.getcwd(), downloader.extract_filename_from_url(pdf_url)))
        else:
            print("❌ 无法打开浏览器")
            print(f"🔗 请手动复制以下URL到浏览器中:")
            print(f"   {pdf_url}")

if __name__ == "__main__":
    main()
