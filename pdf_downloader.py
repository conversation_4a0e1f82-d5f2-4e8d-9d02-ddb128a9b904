#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF文件下载器
用于下载Morningstar网站的PDF文档
"""

import requests
import os
import time
from urllib.parse import urlparse, parse_qs
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PDFDownloader:
    def __init__(self):
        """初始化PDF下载器"""
        self.session = requests.Session()
        # 设置请求头，模拟真实浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/pdf,application/octet-stream,*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        })
        
    def extract_filename_from_url(self, url):
        """从URL中提取文件名信息"""
        try:
            parsed_url = urlparse(url)
            query_params = parse_qs(parsed_url.query)
            
            # 提取关键参数用于构建文件名
            client_id = query_params.get('clientid', ['unknown'])[0]
            investment_id = query_params.get('investmentid', ['unknown'])[0]
            doc_type = query_params.get('documenttype', ['unknown'])[0]
            language = query_params.get('language', ['unknown'])[0]
            
            # 构建有意义的文件名
            filename = f"morningstar_{client_id}_{investment_id}_type{doc_type}_lang{language}.pdf"
            return filename
        except Exception as e:
            logger.warning(f"无法从URL提取文件名: {e}")
            return "morningstar_document.pdf"
    
    def download_pdf(self, url, save_path=None, timeout=30, max_retries=3):
        """
        下载PDF文件

        Args:
            url (str): PDF文件的URL
            save_path (str): 保存路径，如果为None则使用当前目录
            timeout (int): 请求超时时间（秒）
            max_retries (int): 最大重试次数

        Returns:
            str: 下载成功返回文件路径，失败返回None
        """
        if save_path is None:
            filename = self.extract_filename_from_url(url)
            save_path = os.path.join(os.getcwd(), filename)

        logger.info(f"开始下载PDF文件: {url}")
        logger.info(f"保存路径: {save_path}")

        for attempt in range(max_retries):
            try:
                logger.info(f"尝试下载 (第 {attempt + 1} 次)")

                # 添加更多等待时间处理外国网站加载慢的问题
                if attempt > 0:
                    wait_time = 3 + attempt * 2
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)

                # 发送GET请求，添加更多请求头
                headers = {
                    'Referer': 'https://www.morningstar.com/',
                    'Accept': 'application/pdf,text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'DNT': '1',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'same-origin',
                    'Pragma': 'no-cache',
                    'Cache-Control': 'no-cache'
                }
                self.session.headers.update(headers)

                response = self.session.get(url, timeout=timeout, stream=True, allow_redirects=True)
                response.raise_for_status()

                # 检查响应内容类型
                content_type = response.headers.get('content-type', '').lower()
                logger.info(f"响应内容类型: {content_type}")
                logger.info(f"响应状态码: {response.status_code}")
                logger.info(f"最终URL: {response.url}")

                # 检查是否为HTML页面（可能是错误页面或重定向页面）
                if 'text/html' in content_type:
                    logger.warning("响应是HTML页面，可能需要处理JavaScript或登录")
                    # 读取前1000个字符来检查内容
                    content_preview = response.content[:1000].decode('utf-8', errors='ignore')
                    logger.info(f"HTML内容预览: {content_preview[:200]}...")

                    # 如果是HTML页面，尝试查找PDF链接
                    if self._try_extract_pdf_from_html(response, save_path):
                        return save_path
                    else:
                        logger.error("无法从HTML页面中提取PDF文件")
                        continue

                # 检查是否为PDF内容
                if 'application/pdf' not in content_type and 'application/octet-stream' not in content_type:
                    logger.warning(f"响应内容可能不是PDF文件: {content_type}")

                # 获取文件大小
                content_length = response.headers.get('content-length')
                if content_length:
                    file_size = int(content_length)
                    logger.info(f"文件大小: {file_size / 1024 / 1024:.2f} MB")

                # 创建保存目录
                os.makedirs(os.path.dirname(save_path), exist_ok=True)

                # 下载文件
                with open(save_path, 'wb') as f:
                    downloaded = 0
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            downloaded += len(chunk)

                            # 显示下载进度
                            if content_length:
                                progress = (downloaded / file_size) * 100
                                print(f"\r下载进度: {progress:.1f}%", end='', flush=True)

                print()  # 换行

                # 验证下载的文件是否为PDF
                if self._verify_pdf_file(save_path):
                    logger.info(f"PDF文件下载成功: {save_path}")
                    logger.info(f"文件大小: {os.path.getsize(save_path)} 字节")
                    return save_path
                else:
                    logger.error("下载的文件不是有效的PDF文件")
                    os.remove(save_path)  # 删除无效文件
                    continue
                
            except requests.exceptions.Timeout:
                logger.error(f"请求超时 (第 {attempt + 1} 次)")
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt  # 指数退避
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"请求错误 (第 {attempt + 1} 次): {e}")
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                    
            except Exception as e:
                logger.error(f"下载过程中发生错误 (第 {attempt + 1} 次): {e}")
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
        
        logger.error("所有重试都失败了，下载失败")
        return None

    def _verify_pdf_file(self, file_path):
        """验证文件是否为有效的PDF文件"""
        try:
            with open(file_path, 'rb') as f:
                header = f.read(4)
                return header == b'%PDF'
        except Exception as e:
            logger.error(f"验证PDF文件时出错: {e}")
            return False

    def _try_extract_pdf_from_html(self, response, save_path):
        """尝试从HTML页面中提取PDF内容"""
        try:
            # 检查是否有iframe或embed标签指向PDF
            content = response.text

            # 查找可能的PDF链接模式
            import re
            pdf_patterns = [
                r'src=["\']([^"\']*\.pdf[^"\']*)["\']',
                r'href=["\']([^"\']*\.pdf[^"\']*)["\']',
                r'data=["\']([^"\']*\.pdf[^"\']*)["\']',
                r'url\(["\']?([^"\']*\.pdf[^"\']*)["\']?\)',
            ]

            for pattern in pdf_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    logger.info(f"找到可能的PDF链接: {match}")
                    # 尝试下载这个PDF链接
                    if self._download_direct_pdf(match, save_path):
                        return True

            # 如果没有找到PDF链接，检查是否需要JavaScript渲染
            if 'javascript' in content.lower() or 'document.write' in content.lower():
                logger.warning("页面可能需要JavaScript渲染，建议使用浏览器手动下载")

            return False
        except Exception as e:
            logger.error(f"从HTML提取PDF时出错: {e}")
            return False

    def _download_direct_pdf(self, pdf_url, save_path):
        """直接下载PDF URL"""
        try:
            # 处理相对URL
            if pdf_url.startswith('//'):
                pdf_url = 'https:' + pdf_url
            elif pdf_url.startswith('/'):
                pdf_url = 'https://doc.morningstar.com' + pdf_url
            elif not pdf_url.startswith('http'):
                pdf_url = 'https://doc.morningstar.com/' + pdf_url

            logger.info(f"尝试下载直接PDF链接: {pdf_url}")

            response = self.session.get(pdf_url, timeout=30, stream=True)
            response.raise_for_status()

            content_type = response.headers.get('content-type', '').lower()
            if 'application/pdf' in content_type:
                with open(save_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)

                if self._verify_pdf_file(save_path):
                    logger.info(f"成功下载PDF文件: {save_path}")
                    return True

            return False
        except Exception as e:
            logger.error(f"下载直接PDF链接时出错: {e}")
            return False

def main():
    """主函数"""
    # Morningstar PDF文档URL
    pdf_url = "https://doc.morningstar.com/LatestDoc.aspx?clientid=slahk&key=9a89ebce4355d1e3&language=451&investmentid=F000016MLB&documenttype=52&market=1447&investmenttype=1"
    
    # 创建下载器实例
    downloader = PDFDownloader()
    
    # 下载PDF文件
    result = downloader.download_pdf(pdf_url)
    
    if result:
        print(f"\n✅ PDF文件下载成功!")
        print(f"📁 文件路径: {result}")
        print(f"📊 文件大小: {os.path.getsize(result) / 1024 / 1024:.2f} MB")
    else:
        print("\n❌ PDF文件下载失败!")
        print("请检查网络连接或URL是否正确")

if __name__ == "__main__":
    main()
